import mongoose from 'mongoose';

const cartItemSchema = new mongoose.Schema({
    productId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true
    },
    productName: {
        type: String,
        required: true,
        trim: true
    },
    quantity: {
        type: Number,
        required: true,
        min: 1,
        max: 99,
        default: 1
    },
    unitPrice: {
        type: Number,
        required: true,
        min: 0
    },
    totalPrice: {
        type: Number,
        required: true,
        min: 0
    },
    // Cached product data for consistency
    productEmoji: {
        type: String,
        default: null
    },
    productCategory: {
        type: String,
        default: 'digital'
    },
    isDigital: {
        type: Boolean,
        default: false
    }
}, {
    _id: true,
    timestamps: true
});

const shoppingCartSchema = new mongoose.Schema({
    // User and session identification
    userId: {
        type: String, // Discord user ID
        required: true,
        index: true
    },
    userTag: {
        type: String, // Discord user tag for reference
        required: true
    },
    
    // Channel information
    channelId: {
        type: String, // Discord channel ID
        required: true,
        unique: true,
        index: true
    },
    guildId: {
        type: String, // Discord guild ID
        required: true,
        index: true
    },
    
    // Store reference
    storeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Store',
        required: true
    },
    storeName: {
        type: String,
        required: true
    },
    
    // Cart items
    items: [cartItemSchema],
    
    // Calculated values
    subtotal: {
        type: Number,
        default: 0,
        min: 0
    },
    itemCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Cart status
    status: {
        type: String,
        enum: ['active', 'abandoned', 'completed', 'expired'],
        default: 'active',
        index: true
    },
    
    // Session management
    lastActivity: {
        type: Date,
        default: Date.now,
        index: true
    },
    expiresAt: {
        type: Date,
        required: true,
        index: true
    },
    
    // Order reference (when completed)
    orderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Order',
        default: null
    },
    
    // Metadata
    sessionStartedAt: {
        type: Date,
        default: Date.now
    },
    completedAt: {
        type: Date,
        default: null
    }
}, {
    timestamps: true,
    collection: 'shopping_carts'
});

// Índices para otimização
shoppingCartSchema.index({ userId: 1, status: 1 });
shoppingCartSchema.index({ channelId: 1 });
shoppingCartSchema.index({ guildId: 1, status: 1 });
shoppingCartSchema.index({ storeId: 1, status: 1 });
shoppingCartSchema.index({ expiresAt: 1 }); // Para cleanup automático
shoppingCartSchema.index({ lastActivity: 1 }); // Para identificar carrinho inativos

// Middleware para calcular valores automaticamente
shoppingCartSchema.pre('save', function(next) {
    // Calcula subtotal e contagem de itens
    this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
    this.itemCount = this.items.reduce((sum, item) => sum + item.quantity, 0);
    
    // Atualiza última atividade
    this.lastActivity = new Date();
    
    next();
});

// Métodos de instância
shoppingCartSchema.methods.addItem = function(productData, quantity = 1) {
    const { _id, name, price, emoji, category, isDigital } = productData;
    
    // Verifica se o produto já existe no carrinho
    const existingItemIndex = this.items.findIndex(item => 
        item.productId.toString() === _id.toString()
    );
    
    if (existingItemIndex !== -1) {
        // Atualiza quantidade do item existente
        const existingItem = this.items[existingItemIndex];
        existingItem.quantity += quantity;
        existingItem.totalPrice = existingItem.quantity * existingItem.unitPrice;
    } else {
        // Adiciona novo item
        const totalPrice = quantity * price;
        this.items.push({
            productId: _id,
            productName: name,
            quantity,
            unitPrice: price,
            totalPrice,
            productEmoji: emoji,
            productCategory: category,
            isDigital: isDigital || false
        });
    }
    
    return this.save();
};

shoppingCartSchema.methods.removeItem = function(productId) {
    this.items = this.items.filter(item => 
        item.productId.toString() !== productId.toString()
    );
    return this.save();
};

shoppingCartSchema.methods.updateItemQuantity = function(productId, newQuantity) {
    const item = this.items.find(item => 
        item.productId.toString() === productId.toString()
    );
    
    if (item) {
        if (newQuantity <= 0) {
            return this.removeItem(productId);
        }
        
        item.quantity = newQuantity;
        item.totalPrice = item.quantity * item.unitPrice;
        return this.save();
    }
    
    throw new Error('Item não encontrado no carrinho');
};

shoppingCartSchema.methods.clearCart = function() {
    this.items = [];
    return this.save();
};

shoppingCartSchema.methods.markAsCompleted = function(orderId = null) {
    this.status = 'completed';
    this.completedAt = new Date();
    if (orderId) {
        this.orderId = orderId;
    }
    return this.save();
};

shoppingCartSchema.methods.markAsAbandoned = function() {
    this.status = 'abandoned';
    return this.save();
};

shoppingCartSchema.methods.extendExpiration = function(minutes = 60) {
    this.expiresAt = new Date(Date.now() + (minutes * 60 * 1000));
    return this.save();
};

// Métodos estáticos
shoppingCartSchema.statics.findActiveByUser = function(userId, guildId) {
    return this.findOne({ 
        userId, 
        guildId, 
        status: 'active',
        expiresAt: { $gt: new Date() }
    });
};

shoppingCartSchema.statics.findByChannel = function(channelId) {
    return this.findOne({ channelId });
};

shoppingCartSchema.statics.findExpiredCarts = function() {
    return this.find({
        status: 'active',
        expiresAt: { $lt: new Date() }
    });
};

shoppingCartSchema.statics.findInactiveCarts = function(hoursInactive = 2) {
    const cutoffTime = new Date(Date.now() - (hoursInactive * 60 * 60 * 1000));
    return this.find({
        status: 'active',
        lastActivity: { $lt: cutoffTime }
    });
};

shoppingCartSchema.statics.cleanupExpiredCarts = async function() {
    const expiredCarts = await this.findExpiredCarts();
    
    for (const cart of expiredCarts) {
        await cart.markAsAbandoned();
    }
    
    return expiredCarts.length;
};

export default mongoose.model('ShoppingCart', shoppingCartSchema);
