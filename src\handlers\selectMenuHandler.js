import { logger } from '../utils/logger.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import BotConfig from '../models/BotConfig.js';
import ShoppingCart from '../models/ShoppingCart.js';
import { BotLogger } from '../utils/botLogger.js';
import ShoppingChannelManager from '../utils/shoppingChannelManager.js';
import ShoppingCartHandler from './shoppingCartHandler.js';
import { EmbedBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { BOT_CONFIG, COLORS, EMOJIS, VALIDATION } from '../config/constants.js';

/**
 * Manipula interações de menus de seleção
 * @param {StringSelectMenuInteraction} interaction 
 */
export async function handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const selectedValues = interaction.values;
    
    try {
        logger.info(`Menu selecionado: ${customId} por ${interaction.user.tag}, valores: ${selectedValues.join(', ')}`);

        // Roteamento baseado no customId do menu
        if (customId === 'store_product_select') {
            await handleStoreProductSelect(interaction, selectedValues);
            return;
        }

        // Roteamento para carrinho de compras
        if (customId === 'cart_add_product_select') {
            await ShoppingCartHandler.handleProductSelect(interaction);
            return;
        }

        if (customId === 'edit_store_select') {
            await handleEditStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_store_select') {
            await handleDeleteStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'create_product_store_select') {
            await handleCreateProductStoreSelect(interaction, selectedValues);
            return;
        }

        // Handlers para comandos de estoque
        if (customId === 'create_stock_store_select') {
            await handleCreateStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'create_stock_product_select') {
            await handleCreateStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_stock_store_select') {
            await handleEditStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_stock_product_select') {
            await handleEditStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'view_stock_store_select') {
            await handleViewStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'view_stock_product_select') {
            await handleViewStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_stock_store_select') {
            await handleDeleteStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_stock_product_select') {
            await handleDeleteStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_product_store_select') {
            await handleEditProductStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_product_product_select') {
            await handleEditProductProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_product_store_select') {
            await handleDeleteProductStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_product_product_select') {
            await handleDeleteProductProductSelect(interaction, selectedValues);
            return;
        }

        // Handler para seleção de itens de estoque para edição
        if (customId.startsWith('edit_stock_item_select_')) {
            await handleEditStockItemSelect(interaction, selectedValues);
            return;
        }

        // Handler para seleção de itens de estoque para deleção
        if (customId.startsWith('delete_stock_item_select_')) {
            await handleDeleteStockItemSelect(interaction, selectedValues);
            return;
        }

        // Handlers para configuração
        if (customId.startsWith('config_select_')) {
            if (customId === 'config_select_log_type') {
                await handleLogTypeSelect(interaction, selectedValues);
            } else {
                await handleConfigSelectMenu(interaction);
            }
            return;
        }

        // Handler para seleção de emoji
        if (customId === 'emoji_select') {
            await handleEmojiSelect(interaction, selectedValues);
            return;
        }

        const [category, action] = customId.split('_');

        switch (category) {
            case 'store':
                await handleStoreSelectMenu(interaction, action, selectedValues);
                break;
            case 'admin':
                await handleAdminSelectMenu(interaction, action, selectedValues);
                break;
            default:
                logger.warn(`Categoria de menu não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Menu não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar menu ${customId}:`, error);
        
        const errorMessage = {
            content: '❌ Erro ao processar a seleção do menu.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula seleção de produtos na loja - Cria canal de compras privado
 */
async function handleStoreProductSelect(interaction, selectedValues) {
    try {
        const selectedValue = selectedValues[0];

        // Casos especiais
        if (selectedValue === 'disabled') {
            return await interaction.reply({
                content: '📭 Não há produtos disponíveis no momento.',
                ephemeral: true
            });
        }

        if (selectedValue === 'cancel') {
            return await interaction.reply({
                content: '❌ Seleção cancelada.',
                ephemeral: true
            });
        }

        if (selectedValue === 'error') {
            return await interaction.reply({
                content: '❌ Erro ao carregar produtos. Tente novamente mais tarde.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // Busca o produto selecionado
        const product = await Product.findById(selectedValue);

        if (!product) {
            return await interaction.editReply({
                content: '❌ Produto não encontrado.'
            });
        }

        if (product.status !== 'active') {
            return await interaction.editReply({
                content: '❌ Este produto não está mais disponível.'
            });
        }

        // Verifica estoque real através dos StockItems
        const availableStock = await StockItem.countByProduct(product._id, 'available');
        if (availableStock <= 0) {
            return await interaction.editReply({
                content: '❌ Este produto está fora de estoque.'
            });
        }

        // Busca a loja do produto
        const store = await Store.findById(product.storeId);
        if (!store) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada.'
            });
        }

        // Verifica se o usuário já tem um carrinho ativo
        const existingCart = await ShoppingCart.findActiveByUser(interaction.user.id, interaction.guild.id);
        if (existingCart) {
            const existingChannel = interaction.guild.channels.cache.get(existingCart.channelId);
            if (existingChannel) {
                return await interaction.editReply({
                    content: `🛒 Você já tem um carrinho ativo em ${existingChannel}!\n` +
                            `Finalize sua compra atual ou limpe o carrinho para iniciar uma nova sessão.`
                });
            } else {
                // Canal não existe mais, marca carrinho como abandonado
                await existingCart.markAsAbandoned();
            }
        }

        // Cria canal de compras privado
        const channelResult = await ShoppingChannelManager.createShoppingChannel(
            interaction.guild,
            interaction.user,
            store,
            product
        );

        if (!channelResult.success) {
            return await interaction.editReply({
                content: '❌ Erro ao criar canal de compras. Tente novamente.'
            });
        }

        // Incrementa visualizações do produto
        await Product.findByIdAndUpdate(selectedValue, {
            $inc: { views: 1 }
        });

        const emoji = product.emoji || '📦';
        await interaction.editReply({
            content: `✅ **Canal de compras criado!**\n\n` +
                    `${emoji} **${product.name}** foi adicionado ao seu carrinho.\n` +
                    `💰 **Preço:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${product.price.toFixed(2)}\n\n` +
                    `🔗 **Acesse seu carrinho:** ${channelResult.channel}\n` +
                    `⏰ **Sessão expira em:** 2 horas\n\n` +
                    `💡 Use o canal privado para gerenciar seu carrinho e finalizar a compra.`
        });

        logger.info(`Canal de compras criado para ${interaction.user.tag} - Produto: ${product.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto:', error);

        if (interaction.deferred) {
            await interaction.editReply({
                content: '❌ Erro ao processar seleção. Tente novamente.'
            });
        } else {
            await interaction.reply({
                content: '❌ Erro ao processar seleção. Tente novamente.',
                ephemeral: true
            });
        }
    }
}

/**
 * Manipula menus relacionados à loja
 */
async function handleStoreSelectMenu(interaction, action, selectedValues) {
    switch (action) {
        case 'category':
            await interaction.reply({
                content: `🏷️ Categoria selecionada: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        case 'product':
            await interaction.reply({
                content: `📦 Produto selecionado: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de menu de loja não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula menus administrativos
 */
async function handleAdminSelectMenu(interaction, action, selectedValues) {
    // Verificação básica de permissões
    if (!interaction.member.permissions.has('ADMINISTRATOR')) {
        return await interaction.reply({
            content: '❌ Você não tem permissão para usar este menu.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'manage':
            await interaction.reply({
                content: `⚙️ Gerenciando: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de menu administrativo não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula seleção de loja para edição
 */
async function handleEditStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar lojas.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Seleção cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Cria o modal de edição com dados pré-preenchidos
        const modal = new ModalBuilder()
            .setCustomId(`store_edit_${storeId}`)
            .setTitle(`Editar Loja: ${store.name}`);

        // Campo para banner da loja (pré-preenchido)
        const bannerInput = new TextInputBuilder()
            .setCustomId('store_banner')
            .setLabel('Banner da Loja (URL da imagem)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter o atual')
            .setRequired(false)
            .setMaxLength(500)
            .setValue(store.banner);

        // Campo para nome da loja (pré-preenchido)
        const nameInput = new TextInputBuilder()
            .setCustomId('store_name')
            .setLabel('Nome da Loja')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter o atual')
            .setRequired(false)
            .setMaxLength(100)
            .setValue(store.name);

        // Campo para cor da loja (pré-preenchido)
        const colorInput = new TextInputBuilder()
            .setCustomId('store_color')
            .setLabel('Cor da Loja (hex ou nome)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter a atual')
            .setRequired(false)
            .setMaxLength(50)
            .setValue(store.color);

        // Campo para descrição da loja (pré-preenchido)
        const descriptionInput = new TextInputBuilder()
            .setCustomId('store_description')
            .setLabel('Descrição da Loja')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Deixe em branco para manter a atual')
            .setRequired(false)
            .setMaxLength(1000)
            .setValue(store.description);

        // Criação das action rows
        const bannerRow = new ActionRowBuilder().addComponents(bannerInput);
        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const colorRow = new ActionRowBuilder().addComponents(colorInput);
        const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);

        // Adiciona os componentes ao modal
        modal.addComponents(bannerRow, nameRow, colorRow, descriptionRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de edição de loja "${store.name}" exibido para ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para edição:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para edição.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para deleção
 */
async function handleDeleteStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem deletar lojas.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Seleção cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca informações do canal
        const channel = interaction.guild.channels.cache.get(store.channelId);
        const channelInfo = channel ? `#${channel.name}` : `ID: ${store.channelId} (canal não encontrado)`;

        // Cria botões de confirmação
        const confirmButton = new ButtonBuilder()
            .setCustomId(`delete_store_confirm_${storeId}`)
            .setLabel('Confirmar Exclusão')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🗑️');

        const cancelButton = new ButtonBuilder()
            .setCustomId('delete_store_cancel')
            .setLabel('Cancelar')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('❌');

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.reply({
            content: `⚠️ **CONFIRMAÇÃO DE EXCLUSÃO**\n\n` +
                    `Você está prestes a deletar a loja:\n` +
                    `**Nome:** ${store.name}\n` +
                    `**Canal:** ${channelInfo}\n` +
                    `**Criada em:** ${store.createdAt.toLocaleDateString('pt-BR')}\n\n` +
                    `🚨 **Esta ação é IRREVERSÍVEL e irá:**\n` +
                    `• Deletar o canal da loja permanentemente\n` +
                    `• Remover todos os dados da loja do banco de dados\n` +
                    `• Apagar todas as mensagens relacionadas\n\n` +
                    `Tem certeza que deseja continuar?`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Confirmação de deleção da loja "${store.name}" exibida para ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para deleção:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para deleção.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para criação de produto
 */
async function handleCreateProductStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem criar produtos.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de produto cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Cria o modal de criação de produto
        const modal = new ModalBuilder()
            .setCustomId(`product_create_${storeId}`)
            .setTitle(`Criar Produto - ${store.name}`);

        // Campo para nome do produto
        const nameInput = new TextInputBuilder()
            .setCustomId('product_name')
            .setLabel('Nome do Produto')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Digite o nome do produto')
            .setRequired(true)
            .setMinLength(3)
            .setMaxLength(100);

        // Campo para valor do produto
        const priceInput = new TextInputBuilder()
            .setCustomId('product_price')
            .setLabel('Valor do Produto (R$)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 29.99 ou 50')
            .setRequired(true)
            .setMaxLength(20);

        // Campo para emoji do produto (opcional)
        const emojiInput = new TextInputBuilder()
            .setCustomId('product_emoji')
            .setLabel('Emoji do Produto (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 🎮 ou <:custom:123456789>')
            .setRequired(false)
            .setMaxLength(50);

        // Criação das action rows
        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const priceRow = new ActionRowBuilder().addComponents(priceInput);
        const emojiRow = new ActionRowBuilder().addComponents(emojiInput);

        // Adiciona os componentes ao modal
        modal.addComponents(nameRow, priceRow, emojiRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de criação de produto exibido para ${interaction.user.tag} na loja "${store.name}"`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para criação de produto:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para criação do produto.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para criar estoque
 */
async function handleCreateStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos. Crie um produto primeiro usando `/criar-produto`.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos disponíveis
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('create_stock_product_select')
            .setPlaceholder('Selecione o produto para adicionar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const product of products) {
            const stockCount = await StockItem.countByProduct(product._id);
            const statusEmoji = product.status === 'active' ? '✅' :
                               product.status === 'out_of_stock' ? '❌' : '⚠️';

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`${statusEmoji} Estoque atual: ${stockCount} • Preço: R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a criação de estoque')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `📦 **Adicionar Estoque - ${store.name}**\n\nSelecione o produto para adicionar estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Produtos da loja "${store.name}" exibidos para criação de estoque por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para criar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar produtos da loja.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de produto para criar estoque
 */
async function handleCreateStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do valor selecionado
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto selecionado
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        // Cria o modal para adicionar estoque
        const modal = new ModalBuilder()
            .setCustomId(`stock_create_${storeId}_${productId}`)
            .setTitle(`Adicionar Estoque - ${product.name}`);

        // Campo para linhas de estoque
        // Nota: Discord.js limita placeholder a 100 caracteres
        const stockInput = new TextInputBuilder()
            .setCustomId('stock_lines')
            .setLabel('Linhas de Estoque')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Uma linha por item:\<EMAIL>:senha123\<EMAIL>:senha456')
            .setRequired(true)
            .setMaxLength(VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH);

        // Campo para observações (opcional)
        const notesInput = new TextInputBuilder()
            .setCustomId('stock_notes')
            .setLabel('Observações (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Observações administrativas sobre este estoque')
            .setRequired(false)
            .setMaxLength(500);

        // Criação das action rows
        const stockRow = new ActionRowBuilder().addComponents(stockInput);
        const notesRow = new ActionRowBuilder().addComponents(notesInput);

        // Adiciona os componentes ao modal
        modal.addComponents(stockRow, notesRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de criação de estoque exibido para produto "${product.name}" por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para criar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados do produto.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para editar estoque
 */
async function handleEditStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja que têm estoque
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos.',
                components: [],
                ephemeral: true
            });
        }

        // Filtra produtos que têm estoque disponível
        const productsWithStock = [];
        for (const product of products) {
            const stockCount = await StockItem.countByProduct(product._id, 'available');
            if (stockCount > 0) {
                productsWithStock.push({ product, stockCount });
            }
        }

        if (productsWithStock.length === 0) {
            return await interaction.update({
                content: '❌ Nenhum produto desta loja possui estoque disponível para edição.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos que têm estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('edit_stock_product_select')
            .setPlaceholder('Selecione o produto para editar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const { product, stockCount } of productsWithStock) {
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`Estoque disponível: ${stockCount} • Preço: R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a edição de estoque')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `✏️ **Editar Estoque - ${store.name}**\n\nSelecione o produto para editar estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Produtos com estoque da loja "${store.name}" exibidos para edição por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para editar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar produtos da loja.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de produto para editar estoque
 */
async function handleEditStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do valor selecionado
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto selecionado
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        // Busca itens de estoque disponíveis do produto
        const stockItems = await StockItem.find({
            productId: productId,
            status: 'available'
        }).sort({ createdAt: 1 }).limit(25); // Limita a 25 itens para não sobrecarregar o menu

        if (stockItems.length === 0) {
            return await interaction.update({
                content: '❌ Este produto não possui estoque disponível para edição.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os itens de estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`edit_stock_item_select_${storeId}_${productId}`)
            .setPlaceholder('Selecione o item de estoque para editar...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os itens de estoque como opções
        for (let i = 0; i < stockItems.length; i++) {
            const item = stockItems[i];
            const preview = item.content.length > 50 ?
                item.content.substring(0, 47) + '...' :
                item.content;

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(`Item ${i + 1}`)
                    .setDescription(preview)
                    .setValue(item._id.toString())
                    .setEmoji('📝')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a edição')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `✏️ **Editar Estoque - ${product.name}**\n\nSelecione o item de estoque para editar:\n\n` +
                    `📊 **Estoque disponível:** ${stockItems.length} itens\n` +
                    `💰 **Preço:** R$ ${product.price.toFixed(2)}`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Itens de estoque do produto "${product.name}" exibidos para edição por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para editar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar itens de estoque.',
            ephemeral: true
        });
    }
}

// Implementações básicas para os handlers restantes
/**
 * Manipula seleção de loja para visualizar estoque
 */
async function handleViewStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem visualizar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Visualização de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos.',
                components: [],
                ephemeral: true
            });
        }

        // Coleta informações de estoque para todos os produtos
        const productsWithStockInfo = [];
        for (const product of products) {
            const stockSummary = await StockItem.getStockSummary(product._id);
            productsWithStockInfo.push({
                product,
                stockSummary,
                hasStock: stockSummary.total > 0
            });
        }

        // Cria o select menu com os produtos (incluindo os sem estoque para visualização completa)
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('view_stock_product_select')
            .setPlaceholder('Selecione o produto para visualizar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const { product, stockSummary } of productsWithStockInfo) {
            const statusText = stockSummary.available > 0 ?
                `${stockSummary.available} disponível` :
                'Sem estoque';

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`${statusText} • Total: ${stockSummary.total} • R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a visualização')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `👁️ **Visualizar Estoque - ${store.name}**\n\nSelecione o produto para visualizar detalhes do estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Visualização de estoque da loja ${store.name} solicitada por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para visualizar estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula seleção de produto para visualizar estoque detalhado
 */
async function handleViewStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem visualizar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Visualização de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Parse do valor selecionado (formato: storeId|productId)
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto e a loja
        const [product, store] = await Promise.all([
            Product.findById(productId),
            Store.findById(storeId)
        ]);

        if (!product || !store) {
            return await interaction.update({
                content: '❌ Produto ou loja não encontrados.',
                components: [],
                ephemeral: true
            });
        }

        // Busca todos os itens de estoque do produto
        const stockItems = await StockItem.find({ productId }).sort({ createdAt: -1 });
        const stockSummary = await StockItem.getStockSummary(productId);

        // Cria embed com informações detalhadas do estoque
        const embed = new EmbedBuilder()
            .setTitle(`📦 Estoque Detalhado - ${product.name}`)
            .setDescription(`**Loja:** ${store.name}\n**Preço:** R$ ${product.price.toFixed(2)}`)
            .setColor('#0099ff')
            .setTimestamp();

        // Adiciona resumo do estoque
        embed.addFields({
            name: '📊 Resumo do Estoque',
            value: `🟢 **Disponível:** ${stockSummary.available}\n` +
                   `🔴 **Vendido:** ${stockSummary.sold}\n` +
                   `🟡 **Reservado:** ${stockSummary.reserved}\n` +
                   `⚫ **Expirado:** ${stockSummary.expired}\n` +
                   `📈 **Total:** ${stockSummary.total}`,
            inline: true
        });

        // Se há itens disponíveis, mostra alguns exemplos com paginação
        if (stockSummary.available > 0) {
            const availableItems = stockItems.filter(item => item.status === 'available');
            const itemsPerPage = 10;
            const totalPages = Math.ceil(availableItems.length / itemsPerPage);
            const currentPage = 1; // Primeira página por padrão

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageItems = availableItems.slice(startIndex, endIndex);

            const itemsList = pageItems.map((item, index) => {
                const content = item.content.length > 50 ?
                    item.content.substring(0, 50) + '...' :
                    item.content;
                const globalIndex = startIndex + index + 1;
                return `${globalIndex}. \`${content}\``;
            }).join('\n');

            embed.addFields({
                name: `🔍 Itens Disponíveis (Página ${currentPage}/${totalPages})`,
                value: itemsList || 'Nenhum item disponível',
                inline: false
            });

            if (totalPages > 1) {
                embed.addFields({
                    name: '📄 Navegação',
                    value: `Página ${currentPage} de ${totalPages} • Total: ${stockSummary.available} itens`,
                    inline: false
                });
            }
        }

        // Adiciona informações sobre itens vendidos recentemente
        if (stockSummary.sold > 0) {
            const recentSold = stockItems
                .filter(item => item.status === 'sold' && item.soldAt)
                .sort((a, b) => new Date(b.soldAt) - new Date(a.soldAt))
                .slice(0, 3);

            if (recentSold.length > 0) {
                const soldList = recentSold.map(item => {
                    const soldDate = new Date(item.soldAt).toLocaleDateString('pt-BR');
                    return `• Vendido em ${soldDate}`;
                }).join('\n');

                embed.addFields({
                    name: '💰 Vendas Recentes',
                    value: soldList,
                    inline: false
                });
            }
        }

        // Botões para ações adicionais e navegação
        const actionButtons = new ActionRowBuilder();
        const navigationButtons = new ActionRowBuilder();

        if (stockSummary.available > 0) {
            actionButtons.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_edit_stock_${productId}`)
                    .setLabel('✏️ Editar Estoque')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`admin_delete_stock_${productId}`)
                    .setLabel('🗑️ Deletar Itens')
                    .setStyle(ButtonStyle.Danger)
            );
        }

        actionButtons.addComponents(
            new ButtonBuilder()
                .setCustomId(`admin_add_stock_${productId}`)
                .setLabel('➕ Adicionar Estoque')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId(`search_stock_${productId}`)
                .setLabel('🔍 Buscar no Estoque')
                .setStyle(ButtonStyle.Secondary)
        );

        // Adiciona botões de navegação se há múltiplas páginas
        if (stockSummary.available > 10) {
            const availableItems = stockItems.filter(item => item.status === 'available');
            const totalPages = Math.ceil(availableItems.length / 10);

            navigationButtons.addComponents(
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_1`)
                    .setLabel('⏮️ Primeira')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true), // Primeira página está desabilitada
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_${Math.min(2, totalPages)}`)
                    .setLabel('▶️ Próxima')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(totalPages <= 1),
                new ButtonBuilder()
                    .setCustomId(`view_stock_page_${productId}_${totalPages}`)
                    .setLabel('⏭️ Última')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(totalPages <= 1)
            );
        }

        const components = [actionButtons];
        if (navigationButtons.components.length > 0) {
            components.push(navigationButtons);
        }

        await interaction.update({
            content: '',
            embeds: [embed],
            components: components,
            ephemeral: true
        });

        logger.info(`Estoque detalhado do produto ${product.name} visualizado por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para visualizar estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

async function handleDeleteStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Deleção de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja que possuem estoque disponível
        const products = await Product.find({ storeId: storeId });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos.',
                components: [],
                ephemeral: true
            });
        }

        // Verifica quais produtos têm estoque disponível
        const productsWithStock = [];
        for (const product of products) {
            const stockCount = await StockItem.countDocuments({
                productId: product._id,
                status: 'available'
            });

            if (stockCount > 0) {
                productsWithStock.push({
                    ...product.toObject(),
                    stockCount
                });
            }
        }

        if (productsWithStock.length === 0) {
            return await interaction.update({
                content: '❌ Nenhum produto desta loja possui estoque disponível para deletar.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos que têm estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('delete_stock_product_select')
            .setPlaceholder('Selecione o produto para deletar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        productsWithStock.forEach(product => {
            const emoji = product.emoji || '📦';
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(`${product.name}`)
                    .setDescription(`${product.stockCount} itens disponíveis - R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(emoji)
            );
        });

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a deleção de estoque')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `🗑️ **Deletar Estoque - ${store.name}**\n\nSelecione o produto para deletar itens de estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Seleção de loja para deletar estoque: ${store.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para deletar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar produtos da loja.',
            ephemeral: true
        });
    }
}

async function handleDeleteStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Deleção de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do valor selecionado
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto selecionado
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        // Busca itens de estoque disponíveis do produto
        const stockItems = await StockItem.find({
            productId: productId,
            status: 'available'
        }).sort({ createdAt: 1 }).limit(25); // Limita a 25 itens para não sobrecarregar o menu

        if (stockItems.length === 0) {
            return await interaction.update({
                content: '❌ Este produto não possui estoque disponível para deletar.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os itens de estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`delete_stock_item_select_${storeId}_${productId}`)
            .setPlaceholder('Selecione os itens de estoque para deletar...')
            .setMinValues(1)
            .setMaxValues(Math.min(stockItems.length, 25)); // Permite seleção múltipla

        // Adiciona os itens de estoque como opções
        for (let i = 0; i < stockItems.length; i++) {
            const item = stockItems[i];
            const preview = item.content.length > 50 ?
                item.content.substring(0, 47) + '...' :
                item.content;

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(`Item ${i + 1}`)
                    .setDescription(preview)
                    .setValue(item._id.toString())
                    .setEmoji('🗑️')
            );
        }

        // Adiciona opção de deletar todos
        if (stockItems.length > 1) {
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('🗑️ Deletar TODOS os itens')
                    .setDescription(`Deletar todos os ${stockItems.length} itens de estoque`)
                    .setValue('delete_all')
                    .setEmoji('⚠️')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a deleção')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const embed = new EmbedBuilder()
            .setTitle('🗑️ Deletar Itens de Estoque')
            .setDescription(`**Produto:** ${product.name}\n**Loja:** ${store.name}\n**Itens disponíveis:** ${stockItems.length}`)
            .setColor('#ff4444')
            .addFields({
                name: '⚠️ Atenção',
                value: 'Esta ação não pode ser desfeita. Os itens selecionados serão permanentemente removidos do estoque.',
                inline: false
            })
            .setFooter({ text: 'Selecione um ou mais itens para deletar' })
            .setTimestamp();

        await interaction.update({
            content: '',
            embeds: [embed],
            components: [row],
            ephemeral: true
        });

        logger.info(`Seleção de produto para deletar estoque: ${product.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para deletar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar itens de estoque.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de itens específicos de estoque para deleção
 */
async function handleDeleteStockItemSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        // Verifica se o usuário cancelou
        if (selectedValues.includes('cancel')) {
            return await interaction.update({
                content: '❌ Deleção cancelada.',
                components: [],
                embeds: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do customId
        const customId = interaction.customId;
        const parts = customId.split('_');
        const storeId = parts[4];
        const productId = parts[5];

        // Busca informações do produto e loja
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        let itemsToDelete = [];
        let deleteAll = false;

        // Verifica se é para deletar todos
        if (selectedValues.includes('delete_all')) {
            deleteAll = true;
            const allItems = await StockItem.find({
                productId: productId,
                status: 'available'
            });
            itemsToDelete = allItems.map(item => item._id);
        } else {
            // Deleta apenas os itens selecionados
            itemsToDelete = selectedValues;
        }

        if (itemsToDelete.length === 0) {
            return await interaction.reply({
                content: '❌ Nenhum item válido selecionado para deleção.',
                ephemeral: true
            });
        }

        // Busca os itens que serão deletados para mostrar confirmação
        const itemsData = await StockItem.find({
            _id: { $in: itemsToDelete },
            status: 'available'
        });

        if (itemsData.length === 0) {
            return await interaction.reply({
                content: '❌ Nenhum item disponível encontrado para deleção.',
                ephemeral: true
            });
        }

        // Cria embed de confirmação
        const embed = new EmbedBuilder()
            .setTitle('⚠️ Confirmar Deleção de Estoque')
            .setDescription(`**Produto:** ${product.name}\n**Loja:** ${store.name}`)
            .setColor('#ff4444')
            .addFields({
                name: '🗑️ Itens a serem deletados',
                value: deleteAll ?
                    `**TODOS** os ${itemsData.length} itens disponíveis` :
                    `${itemsData.length} item(ns) selecionado(s)`,
                inline: false
            })
            .addFields({
                name: '⚠️ Atenção',
                value: 'Esta ação **NÃO PODE SER DESFEITA**. Os itens serão permanentemente removidos do banco de dados.',
                inline: false
            })
            .setFooter({ text: 'Confirme ou cancele a operação' })
            .setTimestamp();

        // Botões de confirmação
        const confirmButton = new ButtonBuilder()
            .setCustomId(`confirm_delete_stock_${storeId}_${productId}_${itemsToDelete.join(',')}`)
            .setLabel('✅ Confirmar Deleção')
            .setStyle(ButtonStyle.Danger);

        const cancelButton = new ButtonBuilder()
            .setCustomId('cancel_delete_stock')
            .setLabel('❌ Cancelar')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.update({
            content: '',
            embeds: [embed],
            components: [row],
            ephemeral: true
        });

        logger.info(`Confirmação de deleção de estoque solicitada: ${itemsData.length} itens do produto ${product.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de itens para deletar:', error);

        await interaction.reply({
            content: '❌ Erro ao processar seleção de itens.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para editar produto
 */
async function handleEditProductStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar produtos.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de produto cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos para editar.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('edit_product_product_select')
            .setPlaceholder('Selecione o produto para editar...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const product of products) {
            const statusEmoji = product.status === 'active' ? '🟢' :
                               product.status === 'out_of_stock' ? '🔴' : '🟡';

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`${statusEmoji} ${product.status} • R$ ${product.price.toFixed(2)} • Categoria: ${product.category}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a edição de produto')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `✏️ **Editar Produto - ${store.name}**\n\nSelecione o produto que deseja editar:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Seleção de produto para edição na loja ${store.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para editar produto:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula seleção de produto para editar
 */
async function handleEditProductProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar produtos.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de produto cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Parse do valor selecionado (formato: storeId|productId)
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto e a loja
        const [product, store] = await Promise.all([
            Product.findById(productId),
            Store.findById(storeId)
        ]);

        if (!product || !store) {
            return await interaction.update({
                content: '❌ Produto ou loja não encontrados.',
                components: [],
                ephemeral: true
            });
        }

        // Busca informações de estoque
        const stockSummary = await StockItem.getStockSummary(productId);

        // Cria embed com informações do produto
        const embed = new EmbedBuilder()
            .setTitle(`✏️ Editar Produto - ${product.name}`)
            .setDescription(`**Loja:** ${store.name}`)
            .setColor('#ffa500')
            .setTimestamp();

        // Informações básicas do produto
        embed.addFields({
            name: '📋 Informações Atuais',
            value: `**Nome:** ${product.name}\n` +
                   `**Preço:** R$ ${product.price.toFixed(2)}\n` +
                   `**Categoria:** ${product.category}\n` +
                   `**Status:** ${product.status}\n` +
                   `**Emoji:** ${product.emoji || 'Nenhum'}`,
            inline: true
        });

        // Informações de estoque
        embed.addFields({
            name: '📦 Estoque',
            value: `**Disponível:** ${stockSummary.available}\n` +
                   `**Total:** ${stockSummary.total}\n` +
                   `**Vendidos:** ${stockSummary.sold}`,
            inline: true
        });

        // Descrição do produto (se houver)
        if (product.description) {
            const description = product.description.length > 200 ?
                product.description.substring(0, 200) + '...' :
                product.description;
            embed.addFields({
                name: '📝 Descrição',
                value: description,
                inline: false
            });
        }

        // Botões para diferentes tipos de edição
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`edit_product_basic_${productId}`)
                    .setLabel('📝 Editar Informações Básicas')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`edit_product_price_${productId}`)
                    .setLabel('💰 Alterar Preço')
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`edit_product_status_${productId}`)
                    .setLabel('🔄 Alterar Status')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`edit_product_category_${productId}`)
                    .setLabel('📂 Alterar Categoria')
                    .setStyle(ButtonStyle.Secondary)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`view_product_stock_${productId}`)
                    .setLabel('👁️ Ver Estoque Detalhado')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`delete_product_${productId}`)
                    .setLabel('🗑️ Deletar Produto')
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.update({
            content: '',
            embeds: [embed],
            components: [row1, row2, row3],
            ephemeral: true
        });

        logger.info(`Produto ${product.name} selecionado para edição por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para editar:', error);

        const errorMessage = {
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula seleção de tipo de log
 * @param {import('discord.js').StringSelectMenuInteraction} interaction
 * @param {string[]} selectedValues
 */
async function handleLogTypeSelect(interaction, selectedValues) {
    try {
        // Verificação de permissão
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem usar esta configuração.',
                ephemeral: true
            });
        }

        const logType = selectedValues[0];
        const logTypeNames = {
            admin: '🔧 Admin',
            public: '📢 Público',
            error: '❌ Erro',
            moderation: '🛡️ Moderação',
            system: '⚙️ Sistema',
            debug: '🐛 Debug',
            commands: '⚡ Comandos',
            events: '📡 Eventos',
            database: '🗄️ Banco',
            api: '🌐 API'
        };

        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY)
            .setTitle(`${EMOJIS.INFO} Configurar Canal de Log ${logTypeNames[logType]}`)
            .setDescription(`Selecione o canal onde serão enviados os logs do tipo **${logTypeNames[logType]}**.`);

        const selectMenu = new ChannelSelectMenuBuilder()
            .setCustomId(`config_select_log_channel_${logType}`)
            .setPlaceholder('Selecione um canal de texto')
            .setChannelTypes(ChannelType.GuildText);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });

    } catch (error) {
        await logger.logStructured('ERROR', 'EVENT', 'Erro no handler de seleção de tipo de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message,
            stack: error.stack
        });

        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ Ocorreu um erro ao processar a seleção.',
                ephemeral: true
            });
        }
    }
}

/**
 * Manipula select menus de configuração
 * @param {import('discord.js').ChannelSelectMenuInteraction} interaction
 */
async function handleConfigSelectMenu(interaction) {
    try {
        const customId = interaction.customId;
        
        // Verificação de permissão
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem usar esta configuração.',
                ephemeral: true
            });
        }

        const selectedChannel = interaction.channels.first();
        if (!selectedChannel) {
            return await interaction.reply({
                content: '❌ Nenhum canal foi selecionado.',
                ephemeral: true
            });
        }

        // Verifica se o bot tem permissões no canal
        const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
        const permissions = selectedChannel.permissionsFor(botMember);
        
        if (!permissions.has(['ViewChannel', 'SendMessages', 'EmbedLinks'])) {
            return await interaction.reply({
                content: `❌ O bot não tem permissões suficientes no canal ${selectedChannel}. Necessário: Ver Canal, Enviar Mensagens e Inserir Links.`,
                ephemeral: true
            });
        }

        if (customId === 'config_select_admin_channel') {
            await handleAdminChannelSelect(interaction, selectedChannel);
        } else if (customId === 'config_select_public_channel') {
            await handlePublicChannelSelect(interaction, selectedChannel);
        } else if (customId.startsWith('config_select_log_channel_')) {
            const logType = customId.replace('config_select_log_channel_', '');
            await handleLogChannelSelect(interaction, selectedChannel, logType);
        } else {
            logger.warn(`Select menu de configuração não reconhecido: ${customId}`);
            await interaction.reply({
                content: '❌ Configuração não reconhecida.',
                ephemeral: true
            });
        }

    } catch (error) {
        logger.error('Erro no handler de select menu de configuração:', error);
        
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ Ocorreu um erro ao processar a configuração.',
                ephemeral: true
            });
        }
    }
}

/**
 * Configura canal de logs administrativos
 */
async function handleAdminChannelSelect(interaction, channel) {
    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza canal de logs admin
        await config.updateAdminLogChannel(channel.id, interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Canal de Logs Admin Configurado`)
            .setDescription(`O canal ${channel} foi configurado como canal de logs administrativos.`)
            .addFields(
                {
                    name: '📋 Canal Configurado',
                    value: `${channel}`,
                    inline: true
                },
                {
                    name: '🔧 Configurado por',
                    value: `${interaction.user}`,
                    inline: true
                },
                {
                    name: '📊 Tipos de Log',
                    value: '• Criação/Edição de lojas\n• Configurações do bot\n• Ações administrativas\n• Erros do sistema',
                    inline: false
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Envia mensagem de teste no canal configurado
        const testEmbed = new EmbedBuilder()
            .setColor(COLORS.INFO)
            .setTitle(`${EMOJIS.INFO} Canal de Logs Admin Configurado`)
            .setDescription(`Este canal foi configurado como canal de logs administrativos por ${interaction.user}.`)
            .setTimestamp();

        await channel.send({ embeds: [testEmbed] });

        // Log da configuração
        await BotLogger.logConfigChanged(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            'Canal Admin',
            `Canal de logs administrativos configurado: ${channel.name}`
        );

    } catch (error) {
        logger.error('Erro ao configurar canal de logs admin:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle('❌ Erro de Configuração')
            .setDescription('Ocorreu um erro ao configurar o canal público.')
            .setTimestamp();

        await interaction.update({
            embeds: [errorEmbed],
            components: [],
            ephemeral: true
        });
    }
}

async function handleDeleteProductStoreSelect(interaction, selectedValues) {
    try {
        const selectedValue = selectedValues[0];
        
        // Verifica se o usuário cancelou
        if (selectedValue === 'cancel') {
            await interaction.update({
                content: '❌ **Operação cancelada**\n\nA remoção de produto foi cancelada.',
                components: [],
                ephemeral: true
            });
            return;
        }

        const storeId = selectedValue;
        const guildId = interaction.guild.id;

        // Busca a loja selecionada
        const store = await Store.findOne({ _id: storeId, guildId, isActive: true });
        if (!store) {
            await interaction.update({
                content: '❌ Loja não encontrada ou inativa.',
                components: [],
                ephemeral: true
            });
            return;
        }

        // Busca produtos da loja
        const products = await Product.find({
            storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            await interaction.update({
                content: `❌ **Nenhum produto encontrado**\n\nA loja **${store.name}** não possui produtos ativos para remover.`,
                components: [],
                ephemeral: true
            });
            return;
        }

        // Cria o select menu com os produtos
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('delete_product_product_select')
            .setPlaceholder('Selecione o produto para remover...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const product of products) {
            const stockCount = await StockItem.countDocuments({ 
                productId: product._id, 
                status: 'available' 
            });
            
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`💰 ${product.price} • 📦 ${stockCount} em estoque • Criado em ${product.createdAt.toLocaleDateString('pt-BR')}`)
                    .setValue(product._id.toString())
                    .setEmoji('🛍️')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a remoção de produto')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `🗑️ **Deletar Produto - ${store.name}**\n\nSelecione o produto que deseja remover:\n\n⚠️ **Atenção:** Esta ação removerá o produto e todo o estoque associado permanentemente.`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Seleção de loja para deletar produto: ${store.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para deletar produto:', error);
        await interaction.update({
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            components: [],
            ephemeral: true
        });
    }
}

async function handleDeleteProductProductSelect(interaction, selectedValues) {
    try {
        const selectedValue = selectedValues[0];
        
        // Verifica se o usuário cancelou
        if (selectedValue === 'cancel') {
            await interaction.update({
                content: '❌ **Operação cancelada**\n\nA remoção de produto foi cancelada.',
                components: [],
                ephemeral: true
            });
            return;
        }

        const productId = selectedValue;
        const guildId = interaction.guild.id;

        // Busca o produto selecionado
        const product = await Product.findOne({ _id: productId, status: { $ne: 'discontinued' } }).populate('storeId');
        if (!product || product.storeId.guildId !== guildId) {
            await interaction.update({
                content: '❌ Produto não encontrado ou inativo.',
                components: [],
                ephemeral: true
            });
            return;
        }

        // Conta itens de estoque associados
        const stockCount = await StockItem.countDocuments({ 
            productId, 
            status: 'available' 
        });

        // Cria botões de confirmação
        const confirmButton = new ButtonBuilder()
            .setCustomId(`confirm_delete_product_${productId}`)
            .setLabel('✅ Confirmar Remoção')
            .setStyle(ButtonStyle.Danger);

        const cancelButton = new ButtonBuilder()
            .setCustomId('cancel_delete_product')
            .setLabel('❌ Cancelar')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        const embed = new EmbedBuilder()
            .setColor(COLORS.WARNING)
            .setTitle('⚠️ Confirmação de Remoção')
            .setDescription(`Você está prestes a remover o produto **${product.name}** da loja **${product.storeId.name}**.`)
            .addFields(
                { name: '🛍️ Produto', value: product.name, inline: true },
                { name: '💰 Preço', value: product.price, inline: true },
                { name: '🏪 Loja', value: product.storeId.name, inline: true },
                { name: '📦 Estoque Atual', value: `${stockCount} itens`, inline: true },
                { name: '📅 Criado em', value: product.createdAt.toLocaleDateString('pt-BR'), inline: true },
                { name: '⚠️ Atenção', value: 'Esta ação é **irreversível** e removerá:\n• O produto\n• Todo o estoque associado\n• Histórico de vendas relacionado', inline: false }
            )
            .setFooter({ text: 'Esta ação não pode ser desfeita!' })
            .setTimestamp();

        await interaction.update({
            content: '',
            embeds: [embed],
            components: [row],
            ephemeral: true
        });

        logger.info(`Confirmação de remoção solicitada para produto: ${product.name} por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para deletar:', error);
        await interaction.update({
            content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
            components: [],
            ephemeral: true
        });
    }

}

/**
 * Manipula seleção de itens de estoque para edição
 */
async function handleEditStockItemSelect(interaction, selectedValues) {
    try {
        const stockItemId = selectedValues[0];
        
        if (stockItemId === 'cancel') {
            await interaction.update({
                content: '❌ Seleção cancelada.',
                components: [],
                embeds: []
            });
            return;
        }

        // Valida se o ID é um ObjectId válido
        if (!stockItemId || !stockItemId.match(/^[0-9a-fA-F]{24}$/)) {
            await interaction.reply({
                content: '❌ ID do item de estoque inválido.',
                ephemeral: true
            });
            return;
        }

        // Busca o item de estoque
        logger.info(`Buscando item de estoque com ID: ${stockItemId}`);
        const stockItem = await StockItem.findById(stockItemId).populate('productId');
        
        if (!stockItem) {
            logger.warn(`Item de estoque não encontrado: ${stockItemId}`);
            await interaction.reply({
                content: '❌ Item de estoque não encontrado.',
                ephemeral: true
            });
            return;
        }

        if (!stockItem.productId) {
            logger.warn(`Produto não encontrado para o item de estoque: ${stockItemId}`);
            await interaction.reply({
                content: '❌ Produto associado ao item não encontrado.',
                ephemeral: true
            });
            return;
        }

        logger.info(`Item de estoque encontrado: ${stockItem.productId.name}`);

        // Cria modal para edição do item de estoque
        const modal = new ModalBuilder()
            .setCustomId(`edit_stock_item_modal_${stockItemId}`)
            .setTitle(`Editar Item: ${stockItem.productId.name}`);

        const contentInput = new TextInputBuilder()
            .setCustomId('content')
            .setLabel('Conteúdo do Item')
            .setStyle(TextInputStyle.Paragraph)
            .setValue(stockItem.content || '')
            .setRequired(true)
            .setMaxLength(1000);

        const notesInput = new TextInputBuilder()
            .setCustomId('notes')
            .setLabel('Observações (opcional)')
            .setStyle(TextInputStyle.Paragraph)
            .setValue(stockItem.notes || '')
            .setRequired(false)
            .setMaxLength(500);

        const firstActionRow = new ActionRowBuilder().addComponents(contentInput);
        const secondActionRow = new ActionRowBuilder().addComponents(notesInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);

        BotLogger.logAction(
            interaction.guild,
            interaction.user,
            'Edição de Item',
            `Item de estoque selecionado para edição: ${stockItem.productId.name}`
        );

    } catch (error) {
        logger.error('Erro ao processar seleção de item de estoque para edição:', {
            error: error.message,
            stack: error.stack,
            stockItemId: selectedValues[0],
            userId: interaction.user.id,
            guildId: interaction.guild.id
        });
        
        const errorMessage = {
            content: '❌ Erro ao processar a seleção do item.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Configura canal de logs públicas
 */
async function handlePublicChannelSelect(interaction, channel) {
    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza canal de logs públicas
        await config.updatePublicLogChannel(channel.id, interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Canal de Logs Públicas Configurado`)
            .setDescription(`O canal ${channel} foi configurado como canal de logs públicas.`)
            .addFields(
                {
                    name: '📢 Canal Configurado',
                    value: `${channel}`,
                    inline: true
                },
                {
                    name: '🔧 Configurado por',
                    value: `${interaction.user}`,
                    inline: true
                },
                {
                    name: '📊 Tipos de Log',
                    value: '• Vendas concluídas\n• Novos produtos\n• Estatísticas públicas\n• Anúncios do sistema',
                    inline: false
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Envia mensagem de teste no canal configurado
        const testEmbed = new EmbedBuilder()
            .setColor(COLORS.INFO)
            .setTitle(`${EMOJIS.INFO} Canal de Logs Públicas Configurado`)
            .setDescription(`Este canal foi configurado como canal de logs públicas por ${interaction.user}.`)
            .setTimestamp();

        await channel.send({ embeds: [testEmbed] });

        // Log da configuração
        await BotLogger.logConfigChanged(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            'Canal Público',
            `Canal de logs públicas configurado: ${channel.name}`
        );

    } catch (error) {
        logger.error('Erro ao configurar canal de logs públicas:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao configurar o canal de logs públicas.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de canal para logs estruturados
 * @param {import('discord.js').ChannelSelectMenuInteraction} interaction
 * @param {import('discord.js').GuildChannel} channel
 * @param {string} logType
 */
async function handleLogChannelSelect(interaction, channel, logType) {
    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Inicializa logChannels se não existir
        if (!config.logChannels) {
            config.logChannels = {};
        }

        // Define o canal para o tipo especificado
        config.logChannels[logType] = channel.id;

        // Mantém compatibilidade com campos antigos
        if (logType === 'admin') {
            config.adminLogChannelId = channel.id;
        } else if (logType === 'public') {
            config.publicLogChannelId = channel.id;
        }

        config.lastModifiedBy = interaction.user.id;
        await config.save();

        // Limpa cache do botLogger
        const { botLogger } = await import('../utils/botLogger.js');
        botLogger.clearConfigCache(interaction.guild.id);

        const logTypeNames = {
            admin: '🔧 Admin',
            public: '📢 Público',
            error: '❌ Erro',
            moderation: '🛡️ Moderação',
            system: '⚙️ Sistema',
            debug: '🐛 Debug',
            commands: '⚡ Comandos',
            events: '📡 Eventos',
            database: '🗄️ Banco',
            api: '🌐 API'
        };

        const logTypeDescriptions = {
            admin: 'Configurações, criação de lojas, ações administrativas',
            public: 'Vendas concluídas, estatísticas públicas, anúncios',
            error: 'Erros do sistema, falhas críticas',
            moderation: 'Atividades suspeitas, rate limiting, segurança',
            system: 'Inicialização, conexões, status do sistema',
            debug: 'Informações detalhadas para debug',
            commands: 'Execução de comandos, performance',
            events: 'Eventos do Discord, interações',
            database: 'Operações de banco, queries, conexões',
            api: 'Integrações externas, webhooks, APIs'
        };

        await logger.userAction(`Canal de log ${logType} configurado`, {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            logType,
            canal: channel.name,
            canalId: channel.id
        });

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Canal de Log Configurado`)
            .setDescription(`Canal de log **${logTypeNames[logType]}** configurado com sucesso!`)
            .addFields(
                {
                    name: '📍 Canal',
                    value: `${channel}`,
                    inline: true
                },
                {
                    name: '🔧 Configurado por',
                    value: `${interaction.user}`,
                    inline: true
                },
                {
                    name: '📊 Tipos de Log',
                    value: logTypeDescriptions[logType],
                    inline: false
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Envia mensagem de teste no canal configurado
        const testEmbed = new EmbedBuilder()
            .setColor(COLORS.INFO)
            .setTitle(`${EMOJIS.INFO} Canal de Log ${logTypeNames[logType]} Configurado`)
            .setDescription(`Este canal foi configurado como canal de logs **${logTypeNames[logType]}** por ${interaction.user}.`)
            .addFields({
                name: '🧪 Teste',
                value: 'Esta é uma mensagem de teste para verificar se o canal está funcionando corretamente.',
                inline: false
            })
            .setTimestamp();

        await channel.send({ embeds: [testEmbed] });

        // Log da configuração usando o sistema antigo para compatibilidade
        await BotLogger.logConfigChanged(
            interaction.client,
            interaction.guild.id,
            interaction.user,
            `Canal ${logTypeNames[logType]}`,
            `Canal de logs ${logType} configurado: ${channel.name}`
        );

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao configurar canal de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message,
            logType,
            canalId: channel.id
        });

        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao configurar o canal de log.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de emoji para personalização
 */
async function handleEmojiSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem configurar emojis.',
                ephemeral: true
            });
        }

        const emojiKey = selectedValues[0];

        // Importa o emojiManager aqui para evitar dependência circular
        const { emojiManager } = await import('../utils/emojiManager.js');

        // Obtém informações do emoji selecionado
        const availableEmojis = emojiManager.getAvailableEmojis();
        const emojiInfo = availableEmojis[emojiKey];

        if (!emojiInfo) {
            return await interaction.reply({
                content: '❌ Emoji não encontrado.',
                ephemeral: true
            });
        }

        // Obtém emoji atual
        const currentEmoji = await emojiManager.getEmoji(interaction.guild.id, emojiKey);
        const isCustom = currentEmoji !== emojiInfo.emoji;

        // Atualiza a mensagem com informações do emoji selecionado
        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY)
            .setTitle(`🎨 Configurando: ${emojiKey}`)
            .setDescription(`**Descrição:** ${emojiInfo.description}\n**Categoria:** ${emojiInfo.category}`)
            .addFields(
                {
                    name: '📋 Emoji Padrão',
                    value: emojiInfo.emoji,
                    inline: true
                },
                {
                    name: '🔧 Emoji Atual',
                    value: `${currentEmoji}${isCustom ? ' (personalizado)' : ' (padrão)'}`,
                    inline: true
                },
                {
                    name: '📝 Instruções',
                    value: '**Envie uma mensagem** neste canal com o novo emoji que deseja usar.\n\n' +
                           '**Formatos aceitos:**\n' +
                           '• Emojis Unicode: 😀 🎉 ⭐ ✅ ❌\n' +
                           '• Emojis customizados: <:nome:123456789>\n' +
                           '• Emojis animados: <a:nome:123456789>\n\n' +
                           '⏱️ **Tempo limite:** 60 segundos',
                    inline: false
                }
            )
            .setFooter({
                text: `Use emojis do servidor ${interaction.guild.name} para garantir que o bot tenha acesso`,
                iconURL: interaction.guild.iconURL()
            })
            .setTimestamp();

        await interaction.update({
            embeds: [embed],
            components: [],
            ephemeral: true
        });

        // Inicia o coletor de mensagens para capturar o emoji
        await startEmojiMessageCollector(interaction, emojiKey, emojiInfo, currentEmoji);

        logger.info(`Configuração de emoji ${emojiKey} iniciada por ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de emoji:', error);

        const errorMessage = {
            content: '❌ Erro ao processar seleção de emoji.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Inicia um coletor de mensagens para capturar o emoji do usuário
 */
async function startEmojiMessageCollector(interaction, emojiKey, emojiInfo, currentEmoji) {
    try {
        // Importa o emojiManager
        const { emojiManager } = await import('../utils/emojiManager.js');
        const { BotLogger } = await import('../utils/botLogger.js');

        // Cria um filtro para capturar apenas mensagens do usuário que iniciou a configuração
        const filter = (message) => {
            return message.author.id === interaction.user.id && !message.author.bot;
        };

        // Cria o coletor de mensagens com timeout de 60 segundos
        const collector = interaction.channel.createMessageCollector({
            filter,
            max: 1,
            time: 60000 // 60 segundos
        });

        collector.on('collect', async (message) => {
            try {
                const newEmoji = message.content.trim();

                // Valida o novo emoji
                const validation = await emojiManager.validateEmoji(newEmoji, interaction.guild);

                if (!validation.valid) {
                    const errorEmbed = new EmbedBuilder()
                        .setColor(COLORS.ERROR)
                        .setTitle('❌ Emoji Inválido')
                        .setDescription(`**Erro:** ${validation.error}`)
                        .addFields(
                            {
                                name: '📝 Formatos Aceitos',
                                value: '• **Emojis Unicode:** 😀 🎉 ⭐ ✅ ❌\n' +
                                       '• **Emojis Customizados:** <:nome:123456789>\n' +
                                       '• **Emojis Animados:** <a:nome:123456789>',
                                inline: false
                            },
                            {
                                name: '💡 Dicas',
                                value: '• Use emojis do servidor para garantir acesso\n' +
                                       '• Copie emojis customizados com \\:nome: no chat\n' +
                                       '• Teste o emoji antes de confirmar',
                                inline: false
                            }
                        )
                        .setTimestamp();

                    await interaction.followUp({
                        embeds: [errorEmbed],
                        ephemeral: true
                    });
                    return;
                }

                // Verifica se é o mesmo emoji
                if (currentEmoji === newEmoji) {
                    await interaction.followUp({
                        content: `ℹ️ O emoji **${emojiKey}** já está configurado como ${newEmoji}.`,
                        ephemeral: true
                    });
                    return;
                }

                // Atualiza o emoji
                const success = await emojiManager.updateEmoji(
                    interaction.guild.id,
                    emojiKey,
                    newEmoji,
                    interaction.user.id
                );

                if (!success) {
                    await interaction.followUp({
                        content: '❌ Erro interno ao salvar configuração do emoji.',
                        ephemeral: true
                    });
                    return;
                }

                // Cria embed de sucesso
                const successEmbed = new EmbedBuilder()
                    .setColor(COLORS.SUCCESS)
                    .setTitle(`${EMOJIS.SUCCESS} Emoji Configurado`)
                    .setDescription(`O emoji **${emojiKey}** foi atualizado com sucesso!`)
                    .addFields(
                        {
                            name: '📋 Emoji Anterior',
                            value: currentEmoji,
                            inline: true
                        },
                        {
                            name: '🆕 Novo Emoji',
                            value: newEmoji,
                            inline: true
                        },
                        {
                            name: '📊 Informações',
                            value: `**Categoria:** ${emojiInfo.category}\n**Descrição:** ${emojiInfo.description}`,
                            inline: false
                        }
                    )
                    .setFooter({
                        text: `Configurado por ${interaction.user.tag}`,
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await interaction.followUp({
                    embeds: [successEmbed],
                    ephemeral: true
                });

                // Log da alteração
                try {
                    await BotLogger.logConfigChanged(
                        interaction.client,
                        interaction.guild.id,
                        interaction.user,
                        'Emoji',
                        `${emojiKey}: ${currentEmoji} → ${newEmoji}`
                    );
                } catch (logError) {
                    logger.error('Erro ao enviar log de alteração de emoji:', logError);
                }

                // Remove a mensagem do usuário para manter o canal limpo
                try {
                    await message.delete();
                } catch (deleteError) {
                    // Ignora erros de deleção (pode não ter permissão)
                    logger.debug('Não foi possível deletar mensagem do usuário:', deleteError.message);
                }

            } catch (error) {
                logger.error('Erro ao processar emoji coletado:', error);
                await interaction.followUp({
                    content: '❌ Erro interno ao processar o emoji.',
                    ephemeral: true
                });
            }
        });

        collector.on('end', (collected, reason) => {
            if (reason === 'time' && collected.size === 0) {
                // Timeout - usuário não enviou nenhuma mensagem
                interaction.followUp({
                    content: '⏱️ Tempo esgotado! A configuração do emoji foi cancelada. Use o comando `/emojis` novamente para tentar.',
                    ephemeral: true
                }).catch(error => {
                    logger.error('Erro ao enviar mensagem de timeout:', error);
                });
            }
        });

    } catch (error) {
        logger.error('Erro ao iniciar coletor de mensagens para emoji:', error);
        await interaction.followUp({
            content: '❌ Erro interno ao iniciar configuração do emoji.',
            ephemeral: true
        });
    }
}
